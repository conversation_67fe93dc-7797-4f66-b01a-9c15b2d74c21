#ifndef CAMERASTREAM_H
#define CAMERASTREAM_H

#include <QObject>
#include <QDateTime>
#include <QTimer>
#include <gst/app/gstappsink.h>
#include <gst/gst.h>

#define CAMERA "/dev/video11"
#define WIDTH 3840
#define HEIGHT 2160
#define FRAMERATE 60

// 前向声明
class MainWindow;

class CameraStream : public QObject
{
    Q_OBJECT
public:
    explicit CameraStream(QObject *parent = nullptr);
    ~CameraStream();
    bool start_camera();

    // 设备检测方法
    bool checkVideoDevice();
    bool checkResolutionSupport();

    // 录像控制方法
    void startRecording();
    void stopRecording();
    bool isRecording = false;
    QString recordingfilename;



    // 拍照控制方法
    void takePhoto();

    // 清理摄像头数据
    void cleanupCameraData();

    // 通道索引
    int channelstream = 0;
private:
    // GStreamer管道
    GstElement *pipeline = nullptr;
    GstElement *flip=nullptr;

    // Parser元素管理
    GstElement *parser = nullptr;  // parser元素引用
    GstElement *decoder = nullptr; // decoder元素引用
    // 录像相关元素
    GstElement *tee = nullptr;        // 流分流器
    GstElement *queue_record = nullptr;
    GstElement *encoder = nullptr;
    GstElement *h264parse = nullptr;
    GstElement *muxer = nullptr;
    GstElement *filesink = nullptr;
    GstElement *appsink = nullptr;

    // 缓存的录像分支
    GstElement *m_cached_record_branch = nullptr;
    GstPad *m_record_tee_pad = nullptr;
    GstPad *m_record_queue_pad = nullptr;

    // 音频相关元素
    GstElement *m_audio_source = nullptr;      // alsasrc
    GstElement *m_audio_convert = nullptr;     // audioconvert
    GstElement *m_audio_encoder = nullptr;     // voaacenc
    GstElement *m_audio_parser = nullptr;      // aacparse
    GstElement *m_audio_queue = nullptr;       // queue for audio
    QString m_audio_device_path;               // 音频设备路径

    // videoconvert元素
    GstElement *m_convert = nullptr;

    // 拍照相关
    bool m_photoBranchCreated = false;         // 拍照分支是否已创建
    bool m_photoRequested = false;             // 是否请求拍照
    bool m_photoPipelineActive = false;        // 拍照管道是否激活

    // MainWindow指针
    MainWindow* m_mainWindow = nullptr;

    // 录像分支管理方法
    GstElement* createRecordBranch(const QString &filePath);
    void destroyRecordBranch();
    bool activateRecordBranch();
    bool deactivateRecordBranch();

    // 音频分支管理方法
    bool createAudioBranch(const QString &audioDevice);
    void destroyAudioBranch();
    bool activateAudioBranch();



    // 拍照分支管理方法
    GstElement* createPhotoSink();
    void destroyPhotoSink(GstElement* photo_sink);
    void activatePhotoPipeline();
    void deactivatePhotoPipeline();

    // 拍照回调函数
    static GstFlowReturn photoSampleCallback(GstElement *sink, gpointer data);

signals:
    void photoTaken(); // 拍照成功信号
};

#endif // CAMERASTREAM_H
