/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 5.15.11
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QScrollArea>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QStackedWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *centralwidget;
    QWidget *recordingIndicatorWidget;
    QHBoxLayout *recordingIndicatorLayout;
    QSpacerItem *horizontalSpacer_recording;
    QLabel *recordingDotLabel;
    QLabel *recordingTimeLabel;
    QGridLayout *gridLayout_5;
    QVBoxLayout *verticalLayout_3;
    QGridLayout *gridLayout_4;
    QSpacerItem *verticalSpacer_2;
    QSpacerItem *horizontalSpacer_4;
    QSpacerItem *horizontalSpacer_5;
    QStackedWidget *stackedWidget;
    QWidget *menu;
    QGridLayout *gridLayout_3;
    QVBoxLayout *verticalLayout_4;
    QHBoxLayout *horizontalLayout_3;
    QLabel *man_menu;
    QSpacerItem *horizontalSpacer_3;
    QFrame *line;
    QHBoxLayout *horizontalLayout_2;
    QVBoxLayout *verticalLayout_2;
    QPushButton *camera;
    QPushButton *custom;
    QPushButton *versions;
    QPushButton *language;
    QPushButton *recovery_settings;
    QPushButton *Recording;
    QSpacerItem *horizontalSpacer;
    QWidget *recording;
    QGridLayout *gridLayout_7;
    QGridLayout *gridLayout_6;
    QHBoxLayout *horizontalLayout_6;
    QLabel *man_menu_3;
    QSpacerItem *horizontalSpacer_8;
    QHBoxLayout *horizontalLayout_8;
    QPushButton *start_photo;
    QSpacerItem *horizontalSpacer_10;
    QLabel *is_photo;
    QHBoxLayout *horizontalLayout_7;
    QPushButton *start_audio;
    QSpacerItem *horizontalSpacer_9;
    QLabel *is_audio;
    QHBoxLayout *horizontalLayout_5;
    QPushButton *start_watermark;
    QSpacerItem *horizontalSpacer_7;
    QLabel *is_watermark;
    QHBoxLayout *horizontalLayout_4;
    QPushButton *start_recording;
    QSpacerItem *horizontalSpacer_6;
    QLabel *is_record;
    QFrame *line_3;
    QWidget *page;
    QGridLayout *gridLayout_8;
    QScrollArea *scrollArea;
    QWidget *scrollAreaWidgetContents;
    QVBoxLayout *verticalLayout_5;
    QWidget *widget;
    QGridLayout *gridLayout_2;
    QVBoxLayout *verticalLayout;
    QFrame *line_2;
    QHBoxLayout *horizontalLayout;
    QGridLayout *gridLayout;
    QLabel *Return;
    QLabel *select;
    QLabel *reset;
    QLabel *modification;
    QSpacerItem *horizontalSpacer_2;
    QSpacerItem *verticalSpacer;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName(QString::fromUtf8("MainWindow"));
        MainWindow->resize(1223, 717);
        MainWindow->setStyleSheet(QString::fromUtf8(""));
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName(QString::fromUtf8("centralwidget"));
        QSizePolicy sizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(centralwidget->sizePolicy().hasHeightForWidth());
        centralwidget->setSizePolicy(sizePolicy);
        centralwidget->setStyleSheet(QString::fromUtf8(""));
        recordingIndicatorWidget = new QWidget(centralwidget);
        recordingIndicatorWidget->setObjectName(QString::fromUtf8("recordingIndicatorWidget"));
        recordingIndicatorWidget->setGeometry(QRect(0, 0, 200, 60));
        recordingIndicatorWidget->setStyleSheet(QString::fromUtf8("background-color: transparent;"));
        recordingIndicatorLayout = new QHBoxLayout(recordingIndicatorWidget);
        recordingIndicatorLayout->setSpacing(10);
        recordingIndicatorLayout->setObjectName(QString::fromUtf8("recordingIndicatorLayout"));
        recordingIndicatorLayout->setContentsMargins(10, 10, 10, 10);
        horizontalSpacer_recording = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        recordingIndicatorLayout->addItem(horizontalSpacer_recording);

        recordingDotLabel = new QLabel(recordingIndicatorWidget);
        recordingDotLabel->setObjectName(QString::fromUtf8("recordingDotLabel"));
        recordingDotLabel->setMinimumSize(QSize(20, 20));
        recordingDotLabel->setMaximumSize(QSize(20, 20));
        recordingDotLabel->setStyleSheet(QString::fromUtf8("background-color: red;\n"
"border-radius: 10px;"));

        recordingIndicatorLayout->addWidget(recordingDotLabel);

        recordingTimeLabel = new QLabel(recordingIndicatorWidget);
        recordingTimeLabel->setObjectName(QString::fromUtf8("recordingTimeLabel"));
        QFont font;
        font.setPointSize(16);
        font.setBold(true);
        recordingTimeLabel->setFont(font);
        recordingTimeLabel->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: transparent;"));

        recordingIndicatorLayout->addWidget(recordingTimeLabel);

        gridLayout_5 = new QGridLayout(centralwidget);
        gridLayout_5->setSpacing(0);
        gridLayout_5->setObjectName(QString::fromUtf8("gridLayout_5"));
        gridLayout_5->setContentsMargins(0, 0, 0, 0);
        verticalLayout_3 = new QVBoxLayout();
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        gridLayout_4 = new QGridLayout();
        gridLayout_4->setObjectName(QString::fromUtf8("gridLayout_4"));
        verticalSpacer_2 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_4->addItem(verticalSpacer_2, 4, 1, 1, 1);

        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout_4->addItem(horizontalSpacer_4, 2, 0, 1, 1);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout_4->addItem(horizontalSpacer_5, 3, 3, 1, 1);

        stackedWidget = new QStackedWidget(centralwidget);
        stackedWidget->setObjectName(QString::fromUtf8("stackedWidget"));
        stackedWidget->setStyleSheet(QString::fromUtf8(""));
        menu = new QWidget();
        menu->setObjectName(QString::fromUtf8("menu"));
        menu->setStyleSheet(QString::fromUtf8("background-color: rgba(119, 118, 123,127);"));
        gridLayout_3 = new QGridLayout(menu);
        gridLayout_3->setSpacing(0);
        gridLayout_3->setObjectName(QString::fromUtf8("gridLayout_3"));
        gridLayout_3->setContentsMargins(0, 0, 0, 0);
        verticalLayout_4 = new QVBoxLayout();
        verticalLayout_4->setSpacing(10);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        verticalLayout_4->setContentsMargins(-1, -1, -1, 10);
        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        man_menu = new QLabel(menu);
        man_menu->setObjectName(QString::fromUtf8("man_menu"));
        man_menu->setMinimumSize(QSize(0, 50));
        QFont font1;
        font1.setPointSize(31);
        man_menu->setFont(font1);
        man_menu->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        horizontalLayout_3->addWidget(man_menu);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_3->addItem(horizontalSpacer_3);


        verticalLayout_4->addLayout(horizontalLayout_3);

        line = new QFrame(menu);
        line->setObjectName(QString::fromUtf8("line"));
        line->setMinimumSize(QSize(0, 0));
        line->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line->setMidLineWidth(5);
        line->setFrameShape(QFrame::HLine);
        line->setFrameShadow(QFrame::Sunken);

        verticalLayout_4->addWidget(line);

        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        verticalLayout_2 = new QVBoxLayout();
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        camera = new QPushButton(menu);
        camera->setObjectName(QString::fromUtf8("camera"));
        camera->setMinimumSize(QSize(0, 50));
        camera->setFont(font1);
        camera->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        camera->setCheckable(true);
        camera->setAutoRepeat(false);
        camera->setAutoExclusive(false);

        verticalLayout_2->addWidget(camera);

        custom = new QPushButton(menu);
        custom->setObjectName(QString::fromUtf8("custom"));
        custom->setMinimumSize(QSize(0, 50));
        custom->setFont(font1);
        custom->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        custom->setCheckable(true);

        verticalLayout_2->addWidget(custom);

        versions = new QPushButton(menu);
        versions->setObjectName(QString::fromUtf8("versions"));
        versions->setMinimumSize(QSize(0, 50));
        versions->setFont(font1);
        versions->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        versions->setCheckable(true);

        verticalLayout_2->addWidget(versions);

        language = new QPushButton(menu);
        language->setObjectName(QString::fromUtf8("language"));
        language->setMinimumSize(QSize(220, 50));
        language->setFont(font1);
        language->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        language->setCheckable(true);

        verticalLayout_2->addWidget(language);

        recovery_settings = new QPushButton(menu);
        recovery_settings->setObjectName(QString::fromUtf8("recovery_settings"));
        recovery_settings->setMinimumSize(QSize(0, 50));
        recovery_settings->setFont(font1);
        recovery_settings->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        recovery_settings->setCheckable(true);

        verticalLayout_2->addWidget(recovery_settings);

        Recording = new QPushButton(menu);
        Recording->setObjectName(QString::fromUtf8("Recording"));
        Recording->setFont(font1);
        Recording->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        Recording->setCheckable(true);

        verticalLayout_2->addWidget(Recording);


        horizontalLayout_2->addLayout(verticalLayout_2);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer);


        verticalLayout_4->addLayout(horizontalLayout_2);


        gridLayout_3->addLayout(verticalLayout_4, 0, 0, 1, 1);

        stackedWidget->addWidget(menu);
        recording = new QWidget();
        recording->setObjectName(QString::fromUtf8("recording"));
        recording->setStyleSheet(QString::fromUtf8("background-color: rgba(119, 118, 123,127);"));
        gridLayout_7 = new QGridLayout(recording);
        gridLayout_7->setObjectName(QString::fromUtf8("gridLayout_7"));
        gridLayout_6 = new QGridLayout();
        gridLayout_6->setObjectName(QString::fromUtf8("gridLayout_6"));
        gridLayout_6->setHorizontalSpacing(0);
        gridLayout_6->setVerticalSpacing(10);
        gridLayout_6->setContentsMargins(-1, -1, -1, 0);
        horizontalLayout_6 = new QHBoxLayout();
        horizontalLayout_6->setObjectName(QString::fromUtf8("horizontalLayout_6"));
        man_menu_3 = new QLabel(recording);
        man_menu_3->setObjectName(QString::fromUtf8("man_menu_3"));
        man_menu_3->setMinimumSize(QSize(0, 50));
        man_menu_3->setFont(font1);
        man_menu_3->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        horizontalLayout_6->addWidget(man_menu_3);

        horizontalSpacer_8 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_6->addItem(horizontalSpacer_8);


        gridLayout_6->addLayout(horizontalLayout_6, 0, 0, 1, 1);

        horizontalLayout_8 = new QHBoxLayout();
        horizontalLayout_8->setObjectName(QString::fromUtf8("horizontalLayout_8"));
        start_photo = new QPushButton(recording);
        start_photo->setObjectName(QString::fromUtf8("start_photo"));
        start_photo->setMinimumSize(QSize(220, 50));
        start_photo->setMaximumSize(QSize(220, 50));
        start_photo->setFont(font1);
        start_photo->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        start_photo->setCheckable(true);

        horizontalLayout_8->addWidget(start_photo);

        horizontalSpacer_10 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_8->addItem(horizontalSpacer_10);

        is_photo = new QLabel(recording);
        is_photo->setObjectName(QString::fromUtf8("is_photo"));
        is_photo->setMinimumSize(QSize(0, 50));
        is_photo->setFont(font1);
        is_photo->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);\n"
"border:none;"));
        is_photo->setAlignment(Qt::AlignCenter);

        horizontalLayout_8->addWidget(is_photo);


        gridLayout_6->addLayout(horizontalLayout_8, 5, 0, 1, 1);

        horizontalLayout_7 = new QHBoxLayout();
        horizontalLayout_7->setObjectName(QString::fromUtf8("horizontalLayout_7"));
        start_audio = new QPushButton(recording);
        start_audio->setObjectName(QString::fromUtf8("start_audio"));
        start_audio->setMinimumSize(QSize(220, 50));
        start_audio->setMaximumSize(QSize(220, 50));
        start_audio->setFont(font1);
        start_audio->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        start_audio->setCheckable(true);

        horizontalLayout_7->addWidget(start_audio);

        horizontalSpacer_9 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_7->addItem(horizontalSpacer_9);

        is_audio = new QLabel(recording);
        is_audio->setObjectName(QString::fromUtf8("is_audio"));
        is_audio->setMinimumSize(QSize(0, 50));
        is_audio->setFont(font1);
        is_audio->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);\n"
"border:none;"));
        is_audio->setAlignment(Qt::AlignCenter);

        horizontalLayout_7->addWidget(is_audio);


        gridLayout_6->addLayout(horizontalLayout_7, 4, 0, 1, 1);

        horizontalLayout_5 = new QHBoxLayout();
        horizontalLayout_5->setObjectName(QString::fromUtf8("horizontalLayout_5"));
        start_watermark = new QPushButton(recording);
        start_watermark->setObjectName(QString::fromUtf8("start_watermark"));
        start_watermark->setMinimumSize(QSize(220, 50));
        start_watermark->setMaximumSize(QSize(220, 50));
        start_watermark->setFont(font1);
        start_watermark->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        start_watermark->setCheckable(true);

        horizontalLayout_5->addWidget(start_watermark);

        horizontalSpacer_7 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_5->addItem(horizontalSpacer_7);

        is_watermark = new QLabel(recording);
        is_watermark->setObjectName(QString::fromUtf8("is_watermark"));
        is_watermark->setMinimumSize(QSize(0, 50));
        is_watermark->setFont(font1);
        is_watermark->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);\n"
"border:none;"));
        is_watermark->setAlignment(Qt::AlignCenter);

        horizontalLayout_5->addWidget(is_watermark);


        gridLayout_6->addLayout(horizontalLayout_5, 3, 0, 1, 1);

        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        start_recording = new QPushButton(recording);
        start_recording->setObjectName(QString::fromUtf8("start_recording"));
        start_recording->setMinimumSize(QSize(220, 50));
        start_recording->setMaximumSize(QSize(220, 50));
        start_recording->setFont(font1);
        start_recording->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        start_recording->setCheckable(true);

        horizontalLayout_4->addWidget(start_recording);

        horizontalSpacer_6 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_6);

        is_record = new QLabel(recording);
        is_record->setObjectName(QString::fromUtf8("is_record"));
        is_record->setMinimumSize(QSize(0, 50));
        is_record->setFont(font1);
        is_record->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);\n"
"border:none;"));
        is_record->setAlignment(Qt::AlignCenter);

        horizontalLayout_4->addWidget(is_record);


        gridLayout_6->addLayout(horizontalLayout_4, 2, 0, 1, 1);

        line_3 = new QFrame(recording);
        line_3->setObjectName(QString::fromUtf8("line_3"));
        line_3->setMinimumSize(QSize(0, 0));
        line_3->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line_3->setMidLineWidth(5);
        line_3->setFrameShape(QFrame::HLine);
        line_3->setFrameShadow(QFrame::Sunken);

        gridLayout_6->addWidget(line_3, 1, 0, 1, 1);


        gridLayout_7->addLayout(gridLayout_6, 0, 0, 1, 1);

        stackedWidget->addWidget(recording);
        page = new QWidget();
        page->setObjectName(QString::fromUtf8("page"));
        gridLayout_8 = new QGridLayout(page);
        gridLayout_8->setObjectName(QString::fromUtf8("gridLayout_8"));
        scrollArea = new QScrollArea(page);
        scrollArea->setObjectName(QString::fromUtf8("scrollArea"));
        scrollArea->setStyleSheet(QString::fromUtf8("background-color: rgba(119, 118, 123,127);"));
        scrollArea->setWidgetResizable(true);
        scrollAreaWidgetContents = new QWidget();
        scrollAreaWidgetContents->setObjectName(QString::fromUtf8("scrollAreaWidgetContents"));
        scrollAreaWidgetContents->setGeometry(QRect(0, 0, 66, 16));
        scrollArea->setWidget(scrollAreaWidgetContents);

        gridLayout_8->addWidget(scrollArea, 0, 0, 1, 1);

        stackedWidget->addWidget(page);

        gridLayout_4->addWidget(stackedWidget, 1, 1, 1, 1);

        verticalLayout_5 = new QVBoxLayout();
        verticalLayout_5->setSpacing(0);
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        widget = new QWidget(centralwidget);
        widget->setObjectName(QString::fromUtf8("widget"));
        widget->setStyleSheet(QString::fromUtf8("background-color: rgba(119, 118, 123,127);"));
        gridLayout_2 = new QGridLayout(widget);
        gridLayout_2->setSpacing(0);
        gridLayout_2->setObjectName(QString::fromUtf8("gridLayout_2"));
        gridLayout_2->setContentsMargins(0, 0, 0, 0);
        verticalLayout = new QVBoxLayout();
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        line_2 = new QFrame(widget);
        line_2->setObjectName(QString::fromUtf8("line_2"));
        line_2->setMinimumSize(QSize(0, 0));
        line_2->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line_2->setMidLineWidth(5);
        line_2->setFrameShape(QFrame::HLine);
        line_2->setFrameShadow(QFrame::Sunken);

        verticalLayout->addWidget(line_2);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        gridLayout = new QGridLayout();
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        Return = new QLabel(widget);
        Return->setObjectName(QString::fromUtf8("Return"));
        Return->setMinimumSize(QSize(0, 50));
        QFont font2;
        font2.setPointSize(20);
        Return->setFont(font2);
        Return->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        gridLayout->addWidget(Return, 1, 1, 1, 1);

        select = new QLabel(widget);
        select->setObjectName(QString::fromUtf8("select"));
        select->setMinimumSize(QSize(0, 50));
        select->setFont(font2);
        select->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        gridLayout->addWidget(select, 0, 0, 1, 1);

        reset = new QLabel(widget);
        reset->setObjectName(QString::fromUtf8("reset"));
        reset->setMinimumSize(QSize(0, 50));
        reset->setFont(font2);
        reset->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        gridLayout->addWidget(reset, 1, 0, 1, 1);

        modification = new QLabel(widget);
        modification->setObjectName(QString::fromUtf8("modification"));
        modification->setMinimumSize(QSize(0, 50));
        modification->setFont(font2);
        modification->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        gridLayout->addWidget(modification, 0, 1, 1, 1);


        horizontalLayout->addLayout(gridLayout);

        horizontalSpacer_2 = new QSpacerItem(582, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_2);


        verticalLayout->addLayout(horizontalLayout);


        gridLayout_2->addLayout(verticalLayout, 0, 0, 1, 1);


        verticalLayout_5->addWidget(widget);


        gridLayout_4->addLayout(verticalLayout_5, 2, 1, 2, 2);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_4->addItem(verticalSpacer, 0, 1, 1, 1);


        verticalLayout_3->addLayout(gridLayout_4);


        gridLayout_5->addLayout(verticalLayout_3, 0, 0, 1, 1);

        MainWindow->setCentralWidget(centralwidget);

        retranslateUi(MainWindow);

        stackedWidget->setCurrentIndex(0);


        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "MainWindow", nullptr));
        recordingDotLabel->setText(QString());
        recordingTimeLabel->setText(QCoreApplication::translate("MainWindow", "00:00:00", nullptr));
        man_menu->setText(QCoreApplication::translate("MainWindow", "\344\270\273\350\217\234\345\215\225", nullptr));
        camera->setText(QCoreApplication::translate("MainWindow", "\347\233\270\346\234\272\345\217\202\346\225\260", nullptr));
        custom->setText(QCoreApplication::translate("MainWindow", "\347\224\250\346\210\267\350\207\252\345\256\232\344\271\211", nullptr));
        versions->setText(QCoreApplication::translate("MainWindow", "\347\211\210\346\234\254", nullptr));
        language->setText(QCoreApplication::translate("MainWindow", "\350\257\255\350\250\200/Language", nullptr));
        recovery_settings->setText(QCoreApplication::translate("MainWindow", "\346\201\242\345\244\215\345\207\272\345\216\202\350\256\276\347\275\256", nullptr));
        Recording->setText(QCoreApplication::translate("MainWindow", "\345\275\225\345\203\217", nullptr));
        man_menu_3->setText(QCoreApplication::translate("MainWindow", "\345\212\237\350\203\275", nullptr));
        start_photo->setText(QCoreApplication::translate("MainWindow", "\346\213\215\347\205\247", nullptr));
        is_photo->setText(QCoreApplication::translate("MainWindow", "< \346\230\257\345\220\246\346\213\215\347\205\247 >", nullptr));
        start_audio->setText(QCoreApplication::translate("MainWindow", "\351\237\263\351\242\221", nullptr));
        is_audio->setText(QCoreApplication::translate("MainWindow", "< N >", nullptr));
        start_watermark->setText(QCoreApplication::translate("MainWindow", "\346\260\264\345\215\260", nullptr));
        is_watermark->setText(QCoreApplication::translate("MainWindow", "< N >", nullptr));
        start_recording->setText(QCoreApplication::translate("MainWindow", "\345\275\225\345\203\217", nullptr));
        is_record->setText(QCoreApplication::translate("MainWindow", "< N >", nullptr));
        Return->setText(QCoreApplication::translate("MainWindow", "[Menu]\350\277\224\345\233\236", nullptr));
        select->setText(QCoreApplication::translate("MainWindow", "[\342\254\206 \342\254\207]\351\200\211\346\213\251", nullptr));
        reset->setText(QCoreApplication::translate("MainWindow", "[Reset]\347\241\256\345\256\232", nullptr));
        modification->setText(QCoreApplication::translate("MainWindow", "[\342\254\205 \342\236\241]\344\277\256\346\224\271", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
